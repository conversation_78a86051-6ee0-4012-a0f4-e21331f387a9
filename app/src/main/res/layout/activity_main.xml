<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".MainActivity">


    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:layout_editor_absoluteX="0dp"
        tools:layout_editor_absoluteY="-85dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <ImageView
                android:id="@+id/imageView1"
                android:layout_width="match_parent"
                android:layout_height="280dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="44dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="16dp"
                android:contentDescription="@string/input_image" />

            <Button
                android:id="@+id/select_image_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="32dp"
                android:elevation="2dp"
                android:text="Chọn ảnh từ thư viện" />

            <Button
                android:id="@+id/object_detection_button"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="32dp"
                android:layout_marginTop="5dp"
                android:layout_marginEnd="32dp"
                android:elevation="2dp"
                android:text="@string/perform_object_detection" />

            <ImageView
                android:id="@+id/imageView2"
                android:layout_width="match_parent"
                android:layout_height="300dp"
                android:layout_marginStart="16dp"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="16dp"
                android:layout_marginBottom="8dp"
                android:contentDescription="@string/output_image" />
        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>