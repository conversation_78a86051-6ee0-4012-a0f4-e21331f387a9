package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OnnxTensor
import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.util.Log
import java.io.InputStream
import java.nio.FloatBuffer
import kotlin.math.max
import kotlin.math.min

internal data class Result(
    val outputBitmap: Bitmap,
    val boxes: Array<FloatArray> // [x1, y1, x2, y2, score, classId]
)

internal class ObjectDetector(
    private val inputSize: Int = 640,
    private val confidenceThreshold: Float = 0.25f,
    private val iouThreshold: Float = 0.45f
) {

    fun detect(inputStream: InputStream, ortEnv: OrtEnvironment, ortSession: OrtSession): Result {
        // Decode ảnh gốc
        val originalBitmap = BitmapFactory.decodeStream(inputStream)
        val origW = originalBitmap.width
        val origH = originalBitmap.height

        // Resize về 640x640 và normalize
        val resizedBitmap = Bitmap.createScaledBitmap(originalBitmap, inputSize, inputSize, true)
        val chw = bitmapToCHWFloat(resizedBitmap)

        val inputShape = longArrayOf(1, 3, inputSize.toLong(), inputSize.toLong())
        val inputTensor = OnnxTensor.createTensor(ortEnv, FloatBuffer.wrap(chw), inputShape)

        inputTensor.use {
            val output = ortSession.run(mapOf("images" to inputTensor), setOf("output0"))
            output.use {
                @Suppress("UNCHECKED_CAST")
                val raw = (output[0]?.value) as Array<Array<FloatArray>>
                val preds = raw[0] // shape [N, 84] hoặc [8400, classes+5] tuỳ model

                // Hậu xử lý
                val boxes = postProcess(preds, origW, origH)

                val result = Result(originalBitmap, boxes)
                Log.d("ObjectDetector", "Detection result: ${boxes.size} objects")
                return result
            }
        }
    }

    /** Convert Bitmap -> FloatArray CHW [1,3,H,W] normalized 0..1 */
    private fun bitmapToCHWFloat(bitmap: Bitmap): FloatArray {
        val width = bitmap.width
        val height = bitmap.height
        val pixels = IntArray(width * height)
        bitmap.getPixels(pixels, 0, width, 0, 0, width, height)

        val input = FloatArray(3 * height * width)
        var idxR = 0
        var idxG = height * width
        var idxB = 2 * height * width
        for (y in 0 until height) {
            for (x in 0 until width) {
                val c = pixels[y * width + x]
                val r = ((c shr 16) and 0xFF) / 255.0f
                val g = ((c shr 8) and 0xFF) / 255.0f
                val b = (c and 0xFF) / 255.0f
                input[idxR++] = r
                input[idxG++] = g
                input[idxB++] = b
            }
        }
        return input
    }

    /** Hậu xử lý: lọc confidence, rescale, xywh->xyxy, NMS */
    private fun postProcess(
        preds: Array<FloatArray>,
        origW: Int,
        origH: Int
    ): Array<FloatArray> {
        val candidates = mutableListOf<FloatArray>()

        for (row in preds) {
            val objConf = row[4]
            if (objConf < confidenceThreshold) continue

            val classScores = row.copyOfRange(5, row.size)
            val (clsId, clsConf) = argmax(classScores)
            val score = objConf * clsConf
            if (score < confidenceThreshold) continue

            val x = row[0]
            val y = row[1]
            val w = row[2]
            val h = row[3]

            // rescale về kích thước ảnh gốc
            val xScaled = x / inputSize * origW
            val yScaled = y / inputSize * origH
            val wScaled = w / inputSize * origW
            val hScaled = h / inputSize * origH

            // xywh -> xyxy
            val x1 = xScaled - wScaled / 2
            val y1 = yScaled - hScaled / 2
            val x2 = xScaled + wScaled / 2
            val y2 = yScaled + hScaled / 2

            candidates.add(floatArrayOf(x1, y1, x2, y2, score, clsId.toFloat()))
        }

        if (candidates.isEmpty()) return emptyArray()

        // NMS
        val keep = nms(candidates.toTypedArray(), iouThreshold)
        return keep.toTypedArray()
    }

    /** Argmax tiện lợi */
    private fun argmax(arr: FloatArray): Pair<Int, Float> {
        var maxIdx = 0
        var maxVal = arr[0]
        for (i in 1 until arr.size) {
            if (arr[i] > maxVal) {
                maxVal = arr[i]
                maxIdx = i
            }
        }
        return Pair(maxIdx, maxVal)
    }

    /** Non-Max Suppression */
    private fun nms(boxes: Array<FloatArray>, iouThreshold: Float): List<FloatArray> {
        val picked = mutableListOf<FloatArray>()
        val sorted = boxes.sortedByDescending { it[4] }

        val removed = BooleanArray(boxes.size)
        for (i in sorted.indices) {
            if (removed[i]) continue
            val box = sorted[i]
            picked.add(box)

            for (j in (i + 1) until sorted.size) {
                if (removed[j]) continue
                val iou = iou(box, sorted[j])
                if (iou > iouThreshold) removed[j] = true
            }
        }
        return picked
    }

    /** Intersection over Union */
    private fun iou(box1: FloatArray, box2: FloatArray): Float {
        val x1 = max(box1[0], box2[0])
        val y1 = max(box1[1], box2[1])
        val x2 = min(box1[2], box2[2])
        val y2 = min(box1[3], box2[3])
        val inter = max(0f, x2 - x1) * max(0f, y2 - y1)
        val area1 = max(0f, (box1[2] - box1[0])) * max(0f, (box1[3] - box1[1]))
        val area2 = max(0f, (box2[2] - box2[0])) * max(0f, (box2[3] - box2[1]))
        return inter / (area1 + area2 - inter + 1e-6f)
    }
}
