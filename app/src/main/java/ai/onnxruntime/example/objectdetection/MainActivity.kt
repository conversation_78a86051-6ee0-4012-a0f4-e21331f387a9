package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import ai.onnxruntime.extensions.OrtxPackage
import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.graphics.*
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.widget.Button
import android.widget.ImageView
import android.widget.Toast
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import java.io.InputStream

class MainActivity : AppCompatActivity() {

    private var ortEnv: OrtEnvironment = OrtEnvironment.getEnvironment()
    private lateinit var ortSession: OrtSession
    private lateinit var inputImage: ImageView
    private lateinit var outputImage: ImageView
    private lateinit var objectDetectionButton: Button
    private lateinit var selectImageButton: Button
    private lateinit var classes: List<String>
    private var selectedImageUri: Uri? = null
    private lateinit var imagePickerLauncher: ActivityResultLauncher<Intent>
    private var imgIndex: Int = 0

    private val objDetector = ObjectDetector()

    @SuppressLint("UseCompatLoadingForDrawables")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        inputImage = findViewById(R.id.imageView1)
        outputImage = findViewById(R.id.imageView2)
        objectDetectionButton = findViewById(R.id.object_detection_button)
        selectImageButton = findViewById(R.id.select_image_button)

        // Đọc class labels
        classes = readClasses()

        // Khởi tạo ActivityResultLauncher cho việc chọn ảnh
        imagePickerLauncher =
            registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
                if (result.resultCode == Activity.RESULT_OK && result.data != null) {
                    selectedImageUri = result.data?.data
                    selectedImageUri?.let { uri ->
                        try {
                            val bitmap = BitmapFactory.decodeStream(contentResolver.openInputStream(uri))
                            inputImage.setImageBitmap(bitmap)
                            Toast.makeText(this, "Đã chọn ảnh thành công!", Toast.LENGTH_SHORT).show()
                        } catch (e: Exception) {
                            Log.e(TAG, "Lỗi khi tải ảnh: ${e.message}", e)
                            Toast.makeText(this, "Lỗi khi tải ảnh", Toast.LENGTH_SHORT).show()
                        }
                    }
                }
            }

        // Load model ONNX
        val sessionOptions = OrtSession.SessionOptions()
        sessionOptions.registerCustomOpLibrary(OrtxPackage.getLibraryPath())
        ortSession = ortEnv.createSession(readModel(), sessionOptions)

        // Logic cho nút chọn ảnh
        selectImageButton.setOnClickListener {
            val intent = Intent(Intent.ACTION_PICK).apply { type = "image/*" }
            imagePickerLauncher.launch(intent)
        }

        // Logic cho nút detect
        objectDetectionButton.setOnClickListener {
            try {
                performObjectDetection()
            } catch (e: Exception) {
                Log.e(TAG, "Lỗi khi detect: ${e.message}", e)
                Toast.makeText(baseContext, "ObjectDetection thất bại!", Toast.LENGTH_SHORT).show()
            }
        }

        // Hiển thị ảnh mặc định từ assets nếu chưa chọn ảnh
        if (selectedImageUri == null) {
            inputImage.setImageBitmap(BitmapFactory.decodeStream(readInputImage()))
        }
    }

    override fun onDestroy() {
        super.onDestroy()
        ortEnv.close()
        ortSession.close()
    }

    private fun performObjectDetection() {
        val stream: InputStream = if (selectedImageUri != null) {
            contentResolver.openInputStream(selectedImageUri!!)!!
        } else {
            readInputImage()
        }

        val result = objDetector.detect(stream, ortEnv, ortSession)
        updateUI(result)
        Toast.makeText(this, "ObjectDetection xong!", Toast.LENGTH_SHORT).show()
    }

    private fun updateUI(result: Result) {
        val mutableBitmap: Bitmap = result.outputBitmap.copy(Bitmap.Config.ARGB_8888, true)
        val canvas = Canvas(mutableBitmap)
        val paintBox = Paint().apply {
            color = Color.RED
            style = Paint.Style.STROKE
            strokeWidth = 4f
        }
        val paintText = Paint().apply {
            color = Color.YELLOW
            textSize = 32f
            style = Paint.Style.FILL
        }

        for (box in result.boxes) {
            val x1 = box[0]
            val y1 = box[1]
            val x2 = box[2]
            val y2 = box[3]
            val score = box[4]
            val clsId = box[5].toInt()

            // Vẽ bounding box
            canvas.drawRect(x1, y1, x2, y2, paintBox)

            // Vẽ nhãn
            val label = "%s %.2f".format(classes.getOrElse(clsId) { "cls$clsId" }, score)
            canvas.drawText(label, x1, y1 - 10, paintText)
        }

        outputImage.setImageBitmap(mutableBitmap)
    }

    private fun readModel(): ByteArray {
        val modelID = R.raw.best  // model onnx bạn để trong res/raw/best.onnx
        return resources.openRawResource(modelID).readBytes()
    }

    private fun readClasses(): List<String> {
        return resources.openRawResource(R.raw.classes).bufferedReader().readLines()
    }

    private fun readInputImage(): InputStream {
        return assets.open("anh_${imgIndex++}.jpg") // fallback ảnh test trong assets
    }

    companion object {
        const val TAG = "ORTObjectDetection"
    }
}
