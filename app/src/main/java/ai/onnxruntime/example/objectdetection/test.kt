package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import android.util.Log
import org.junit.Test
import org.junit.Assert.*

class Test {
  @Test
  fun test() {
    // Load model từ assets
    val modelStream = javaClass.classLoader!!.getResourceAsStream("best.onnx")!!
    val modelBytes = modelStream.readBytes()

    val env = OrtEnvironment.getEnvironment()
    val session: OrtSession = env.createSession(modelBytes)

    // Load ảnh test
    val imgStream = javaClass.classLoader!!.getResourceAsStream("img.png")!!

    // Gọi ObjectDetector
    val detector = ObjectDetector()
    val result = detector.detect(imgStream, env, session)

    // In log kết quả (dùng Log.d thay vì println)
    Log.d("ObjectDetectorTest", "Detected objects: ${result.boxes.size}")
    for (box in result.boxes) {
      Log.d("ObjectDetectorTest", "Box: x1=${box[0]}, y1=${box[1]}, x2=${box[2]}, y2=${box[3]}, score=${box[4]}, classId=${box[5]}")
    }

    // Assert tối thiểu: phải có ít nhất 1 box
    assertTrue("Không tìm thấy object nào", result.boxes.isNotEmpty())
  }
}