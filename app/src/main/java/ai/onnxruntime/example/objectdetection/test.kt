package ai.onnxruntime.example.objectdetection

import ai.onnxruntime.OrtEnvironment
import ai.onnxruntime.OrtSession
import org.junit.Test
import org.junit.Assert.*
import kotlin.system.measureTimeMillis

class ModelQualityTest {

    companion object {
        private const val CONFIDENCE_THRESHOLD = 0.5f
        private const val MIN_DETECTION_COUNT = 1
        private const val MAX_INFERENCE_TIME_MS = 5000L // 5 giây
    }

    @Test
    fun testModelInference() {
        println("=== BẮT ĐẦU TEST CHẤT LƯỢNG MODEL ===")

        // Load model từ assets
        val modelStream = javaClass.classLoader!!.getResourceAsStream("best.onnx")!!
        val modelBytes = modelStream.readBytes()
        println("✓ Đã load model thành công (${modelBytes.size} bytes)")

        val env = OrtEnvironment.getEnvironment()
        val session: OrtSession = env.createSession(modelBytes)
        println("✓ Đã khởi tạo ONNX session thành công")

        // Load ảnh test
        val imgStream = javaClass.classLoader!!.getResourceAsStream("img.png")!!
        println("✓ Đã load ảnh test thành công")

        // Đo thời gian inference
        val detector = ObjectDetector()
        var result: Result
        val inferenceTime = measureTimeMillis {
            result = detector.detect(imgStream, env, session)
        }

        // In kết quả chi tiết
        println("\n=== KẾT QUẢ DETECTION ===")
        println("Thời gian inference: ${inferenceTime}ms")
        println("Số objects phát hiện: ${result.boxes.size}")

        if (result.boxes.isNotEmpty()) {
            println("\nChi tiết các objects:")
            result.boxes.forEachIndexed { index, box ->
                val confidence = box[4]
                val classId = box[5].toInt()
                println("Object ${index + 1}: " +
                       "x1=${String.format("%.2f", box[0])}, " +
                       "y1=${String.format("%.2f", box[1])}, " +
                       "x2=${String.format("%.2f", box[2])}, " +
                       "y2=${String.format("%.2f", box[3])}, " +
                       "confidence=${String.format("%.3f", confidence)}, " +
                       "classId=$classId")
            }
        }

        // Đánh giá chất lượng
        evaluateModelQuality(result, inferenceTime)

        // Cleanup
        session.close()
        env.close()
        println("\n✓ Đã cleanup resources thành công")
    }

    private fun evaluateModelQuality(result: Result, inferenceTime: Long) {
        println("\n=== ĐÁNH GIÁ CHẤT LƯỢNG MODEL ===")

        // Test 1: Kiểm tra có phát hiện được object không
        assertTrue("❌ Model không phát hiện được object nào", result.boxes.isNotEmpty())
        println("✓ Model phát hiện được ${result.boxes.size} objects")

        // Test 2: Kiểm tra thời gian inference
        assertTrue("❌ Thời gian inference quá chậm: ${inferenceTime}ms > ${MAX_INFERENCE_TIME_MS}ms",
                  inferenceTime <= MAX_INFERENCE_TIME_MS)
        println("✓ Thời gian inference hợp lý: ${inferenceTime}ms")

        // Test 3: Kiểm tra confidence score
        val highConfidenceBoxes = result.boxes.filter { it[4] >= CONFIDENCE_THRESHOLD }
        println("✓ Có ${highConfidenceBoxes.size}/${result.boxes.size} objects với confidence >= $CONFIDENCE_THRESHOLD")

        // Test 4: Kiểm tra tính hợp lệ của bounding boxes
        var validBoxes = 0
        result.boxes.forEach { box ->
            val x1 = box[0]
            val y1 = box[1]
            val x2 = box[2]
            val y2 = box[3]

            if (x1 < x2 && y1 < y2 && x1 >= 0 && y1 >= 0) {
                validBoxes++
            }
        }

        assertTrue("❌ Có bounding boxes không hợp lệ", validBoxes == result.boxes.size)
        println("✓ Tất cả ${validBoxes} bounding boxes đều hợp lệ")

        // Tính toán metrics
        calculateMetrics(result)
    }

    private fun calculateMetrics(result: Result) {
        println("\n=== METRICS CHI TIẾT ===")

        if (result.boxes.isEmpty()) {
            println("Không có objects để tính metrics")
            return
        }

        // Confidence statistics
        val confidences = result.boxes.map { it[4] }
        val avgConfidence = confidences.average()
        val maxConfidence = confidences.maxOrNull() ?: 0f
        val minConfidence = confidences.minOrNull() ?: 0f

        println("Confidence trung bình: ${String.format("%.3f", avgConfidence)}")
        println("Confidence cao nhất: ${String.format("%.3f", maxConfidence)}")
        println("Confidence thấp nhất: ${String.format("%.3f", minConfidence)}")

        // Class distribution
        val classIds = result.boxes.map { it[5].toInt() }
        val classDistribution = classIds.groupingBy { it }.eachCount()
        println("Phân bố classes:")
        classDistribution.forEach { (classId, count) ->
            println("  Class $classId: $count objects")
        }

        // Box size analysis
        val boxSizes = result.boxes.map { box ->
            val width = box[2] - box[0]
            val height = box[3] - box[1]
            width * height
        }
        val avgBoxSize = boxSizes.average()
        println("Kích thước box trung bình: ${String.format("%.2f", avgBoxSize)}")

        println("\n=== HOÀN THÀNH TEST ===")
    }
}